# Proxmox Migration Preparation Script
# Run this on Windows before converting to VM

# Create migration directory
$migrationPath = "C:\ProxmoxMigration"
Write-Host "Creating migration directory: $migrationPath"
New-Item -ItemType Directory -Path $migrationPath -Force

# Backup Docker Compose configuration
Write-Host "Backing up Docker Compose configuration..."
$dockerPath = Get-Location  # Use current directory since we're in the Abacus folder
Write-Host "Source path: $dockerPath"
Copy-Item -Path "$dockerPath\*" -Destination "$migrationPath\docker-compose" -Recurse -Force

# Create storage mapping file
$storageMapping = @"
# Storage Mapping for Proxmox Migration
# Based on your PCPartPicker build

# Current Windows Drives → Proxmox Storage Pools
# C:\ (Windows) → vm-fast pool (Samsung 980 Pro 1TB)
# D:\ → vm-apps pool (Samsung 970 Evo Plus 1TB)  
# E:\ → model-cache pool (Samsung 750 EVO 250GB)
# F:\ → bulk-storage pool (WD Purple 4TB + Toshiba 1TB)

# Docker Data Migration Paths:
./data/vllm → /mnt/model-cache/vllm
./data/comfyui → /mnt/model-cache/comfyui
./data/whisperx → /mnt/model-cache/whisperx
./data/nextcloud_data → /mnt/bulk-storage/nextcloud
./data/open_webui → /mnt/vm-apps/open-webui
./repos → /mnt/vm-apps/repos

# VM Allocation Plan:
# vm-fast pool: AI Inference VMs (RTX 3090)
# vm-apps pool: Web Services + Databases
# model-cache pool: AI Models + Cache
# bulk-storage pool: Nextcloud + Backups
"@

$storageMapping | Out-File -FilePath "$migrationPath\storage-mapping.txt"

# Create VM configuration templates
$vmConfigs = @"
# VM Configuration Templates

## AI-Inference-Primary (RTX 3090)
- CPU: 8 cores
- RAM: 16GB
- Storage: vm-fast pool (400GB)
- GPU: RTX 3090 passthrough
- Services: vLLM, ComfyUI

## AI-Inference-Secondary (GTX 1060)  
- CPU: 4 cores
- RAM: 8GB
- Storage: vm-apps pool (200GB)
- GPU: GTX 1060 passthrough
- Services: WhisperX, Agent-Zero

## Web-Services
- CPU: 4 cores
- RAM: 8GB
- Storage: vm-apps pool (200GB)
- Services: Open WebUI, LibreChat, Flowise

## Database-Cluster
- CPU: 4 cores
- RAM: 12GB
- Storage: vm-apps pool (300GB)
- Services: MongoDB, PostgreSQL, MeiliSearch

## Storage-Services
- CPU: 2 cores
- RAM: 4GB
- Storage: bulk-storage pool (1TB)
- Services: Nextcloud, file sharing
"@

$vmConfigs | Out-File -FilePath "$migrationPath\vm-configurations.txt"

# Create migration checklist
$checklist = @"
# Proxmox Migration Checklist

## Pre-Migration (Windows)
[ ] Run this script to backup configurations
[ ] Create Windows VM backup (Disk2VHD or dd)
[ ] Document current network settings
[ ] Export Docker volumes
[ ] Backup important files to external drive

## Proxmox Setup
[ ] Configure ZFS storage pools
[ ] Set up network bridges
[ ] Enable GPU passthrough (IOMMU)
[ ] Create VM templates
[ ] Import Windows VM

## Service Migration
[ ] Deploy AI Inference VMs
[ ] Deploy Web Services VMs  
[ ] Deploy Database VMs
[ ] Configure networking between VMs
[ ] Test all services

## Post-Migration
[ ] Verify all services working
[ ] Set up monitoring
[ ] Configure backups
[ ] Update DNS/Tailscale
"@

$checklist | Out-File -FilePath "$migrationPath\migration-checklist.txt"

Write-Host "Migration preparation complete!"
Write-Host "Files created in: $migrationPath"
Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Copy this folder to external drive or network share"
Write-Host "2. Create Windows VM backup"
Write-Host "3. Boot into Proxmox for storage setup"
