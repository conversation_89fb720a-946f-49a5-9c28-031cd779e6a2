# Essential Files Backup Script
# Backs up only the critical configuration files, avoiding cache and model files

$migrationPath = "C:\ProxmoxMigration"
$backupPath = "$migrationPath\essential-backup"

Write-Host "Creating essential backup directory..."
New-Item -ItemType Directory -Path $backupPath -Force

# Copy essential configuration files
Write-Host "Backing up Docker Compose files..."
Copy-Item "docker-compose.yaml" "$backupPath\" -Force
Copy-Item "docker-compose.override.yaml" "$backupPath\" -Force
Copy-Item ".env" "$backupPath\" -Force

# Copy Dockerfiles
Write-Host "Backing up Dockerfiles..."
Get-ChildItem -Name "Dockerfile*" | ForEach-Object {
    Copy-Item $_ "$backupPath\" -Force
}

# Copy scripts and documentation
Write-Host "Backing up scripts and docs..."
Get-ChildItem -Name "*.md" | ForEach-Object {
    Copy-Item $_ "$backupPath\" -Force
}
Get-ChildItem -Name "*.sh" | ForEach-Object {
    Copy-Item $_ "$backupPath\" -Force
}
Get-ChildItem -Name "*.bat" | ForEach-Object {
    Copy-Item $_ "$backupPath\" -Force
}
Get-ChildItem -Name "*.py" | ForEach-Object {
    Copy-Item $_ "$backupPath\" -Force
}

# Copy configuration directories (avoiding cache)
Write-Host "Backing up configuration directories..."

# Create data backup structure
$dataBackup = "$backupPath\data-configs"
New-Item -ItemType Directory -Path $dataBackup -Force

# Copy important config files from data directories
$configDirs = @(
    "data\tailscale",
    "data\caddy", 
    "data\flowise",
    "data\n8n",
    "data\searxng",
    "data\perplexica"
)

foreach ($dir in $configDirs) {
    if (Test-Path $dir) {
        Write-Host "Backing up $dir..."
        $destDir = "$dataBackup\$(Split-Path $dir -Leaf)"
        New-Item -ItemType Directory -Path $destDir -Force
        
        # Copy config files only, skip cache/models
        Get-ChildItem $dir -File -Recurse | Where-Object {
            $_.Extension -in @('.yaml', '.yml', '.json', '.conf', '.cfg', '.toml', '.env', '.txt') -and
            $_.DirectoryName -notlike "*cache*" -and
            $_.DirectoryName -notlike "*models*" -and
            $_.DirectoryName -notlike "*temp*"
        } | ForEach-Object {
            $relativePath = $_.FullName.Substring((Resolve-Path $dir).Path.Length + 1)
            $destFile = Join-Path $destDir $relativePath
            $destFileDir = Split-Path $destFile -Parent
            if (!(Test-Path $destFileDir)) {
                New-Item -ItemType Directory -Path $destFileDir -Force
            }
            Copy-Item $_.FullName $destFile -Force
        }
    }
}

# Copy repos directory structure (configs only)
Write-Host "Backing up repos configurations..."
if (Test-Path "repos") {
    $reposBackup = "$backupPath\repos-configs"
    New-Item -ItemType Directory -Path $reposBackup -Force
    
    Get-ChildItem "repos" -Directory | ForEach-Object {
        $repoName = $_.Name
        $repoBackupDir = "$reposBackup\$repoName"
        New-Item -ItemType Directory -Path $repoBackupDir -Force
        
        # Copy important config files from each repo
        Get-ChildItem $_.FullName -File -Recurse | Where-Object {
            $_.Extension -in @('.yaml', '.yml', '.json', '.conf', '.cfg', '.toml', '.env', '.txt', '.md') -and
            $_.DirectoryName -notlike "*node_modules*" -and
            $_.DirectoryName -notlike "*cache*" -and
            $_.DirectoryName -notlike "*models*" -and
            $_.Name -notlike "*.log"
        } | Select-Object -First 50 | ForEach-Object {  # Limit to first 50 files per repo
            $relativePath = $_.FullName.Substring((Resolve-Path $_.Directory.Parent.FullName).Path.Length + 1)
            $destFile = Join-Path $repoBackupDir $relativePath
            $destFileDir = Split-Path $destFile -Parent
            if (!(Test-Path $destFileDir)) {
                New-Item -ItemType Directory -Path $destFileDir -Force
            }
            Copy-Item $_.FullName $destFile -Force
        }
    }
}

# Copy MCP servers configs
Write-Host "Backing up MCP servers..."
if (Test-Path "mcp_servers") {
    Copy-Item "mcp_servers" "$backupPath\mcp_servers" -Recurse -Force
}

# Copy pipelines
Write-Host "Backing up pipelines..."
if (Test-Path "pipelines") {
    $pipelinesBackup = "$backupPath\pipelines"
    New-Item -ItemType Directory -Path $pipelinesBackup -Force
    
    Get-ChildItem "pipelines" -File -Recurse | Where-Object {
        $_.Extension -in @('.py', '.yaml', '.yml', '.json', '.txt', '.md') -and
        $_.DirectoryName -notlike "*__pycache__*"
    } | ForEach-Object {
        $relativePath = $_.FullName.Substring((Resolve-Path "pipelines").Path.Length + 1)
        $destFile = Join-Path $pipelinesBackup $relativePath
        $destFileDir = Split-Path $destFile -Parent
        if (!(Test-Path $destFileDir)) {
            New-Item -ItemType Directory -Path $destFileDir -Force
        }
        Copy-Item $_.FullName $destFile -Force
    }
}

Write-Host ""
Write-Host "Essential backup complete!"
Write-Host "Backup location: $backupPath"
Write-Host ""
Write-Host "Files backed up:"
Get-ChildItem $backupPath -Recurse | Measure-Object | ForEach-Object { Write-Host "Total files: $($_.Count)" }
Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Copy C:\ProxmoxMigration to external drive"
Write-Host "2. Create Windows system backup (Disk2VHD)"
Write-Host "3. Boot into Proxmox for storage setup"
