#!/bin/bash
# Proxmox Storage Setup Script
# Run this after booting into Proxmox

echo "=== Proxmox Storage Configuration ==="
echo "Based on your PCPartPicker build:"
echo "- Samsung 980 Pro 1TB NVMe (Primary)"
echo "- Samsung 970 Evo Plus 1TB NVMe (Secondary)"  
echo "- Samsung 750 EVO 250GB SSD"
echo "- WD Purple 4TB HDD + Toshiba 1TB HDD"
echo ""

# Check current storage
echo "Current storage layout:"
lsblk
echo ""

# Check existing ZFS pools
echo "Existing ZFS pools:"
zpool status
echo ""

# Identify drives (you'll need to adjust these based on actual device names)
echo "Please verify these device names match your setup:"
echo "/dev/nvme0n1 - Samsung 980 Pro 1TB (should have Proxmox installed)"
echo "/dev/nvme1n1 - Samsung 970 Evo Plus 1TB"
echo "/dev/sda - Samsung 750 EVO 250GB"
echo "/dev/sdb - WD Purple 4TB"
echo "/dev/sdc - Toshiba 1TB"
echo ""

read -p "Do the device names look correct? (y/n): " confirm
if [[ $confirm != "y" ]]; then
    echo "Please check 'lsblk' output and adjust device names in this script"
    exit 1
fi

# Create ZFS pools
echo "Creating ZFS storage pools..."

# Pool 1: Fast VM storage (remaining space on 980 Pro)
# Note: Proxmox is already installed, so we'll use remaining space
echo "Creating vm-fast pool on remaining 980 Pro space..."
# This assumes Proxmox used part of nvme0n1, we'll create a partition for the pool
# You may need to adjust this based on your Proxmox installation

# Pool 2: Application storage (970 Evo Plus)
echo "Creating vm-apps pool..."
zpool create vm-apps /dev/nvme1n1

# Pool 3: Model cache (750 EVO SSD)
echo "Creating model-cache pool..."
zpool create model-cache /dev/sda

# Pool 4: Bulk storage (Purple 4TB + Toshiba 1TB)
echo "Creating bulk-storage pool with redundancy..."
zpool create bulk-storage mirror /dev/sdb /dev/sdc

# Add pools to Proxmox storage
echo "Adding pools to Proxmox storage configuration..."

pvesm add zfspool vm-apps --pool vm-apps --content images,rootdir,vztmpl
pvesm add zfspool model-cache --pool model-cache --content snippets,backup
pvesm add zfspool bulk-storage --pool bulk-storage --content backup,images,iso

# Set up datasets for organization
echo "Creating ZFS datasets..."

# VM Apps datasets
zfs create vm-apps/web-services
zfs create vm-apps/databases  
zfs create vm-apps/automation

# Model cache datasets
zfs create model-cache/vllm
zfs create model-cache/comfyui
zfs create model-cache/whisperx
zfs create model-cache/shared

# Bulk storage datasets
zfs create bulk-storage/nextcloud
zfs create bulk-storage/backups
zfs create bulk-storage/media

# Set up NFS shares for model access
echo "Setting up NFS shares for model storage..."
zfs set sharenfs=on model-cache/vllm
zfs set sharenfs=on model-cache/comfyui
zfs set sharenfs=on model-cache/whisperx
zfs set sharenfs=on model-cache/shared

# Configure compression and deduplication
echo "Optimizing ZFS settings..."
zfs set compression=lz4 vm-apps
zfs set compression=lz4 model-cache
zfs set compression=gzip bulk-storage

# Show final configuration
echo ""
echo "=== Final Storage Configuration ==="
zpool status
echo ""
pvesm status
echo ""
echo "ZFS datasets:"
zfs list
echo ""
echo "Storage setup complete!"
echo ""
echo "Next steps:"
echo "1. Set up network bridges"
echo "2. Configure GPU passthrough"
echo "3. Create VM templates"
echo "4. Import Windows VM"
