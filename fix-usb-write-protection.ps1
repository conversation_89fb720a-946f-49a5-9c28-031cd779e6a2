# Fix USB Write Protection Script
# Run as Administrator

Write-Host "=== USB Write Protection Fix ==="
Write-Host ""

# Method 1: Check current USB drives
Write-Host "Current USB drives detected:"
Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 2} | ForEach-Object {
    Write-Host "$($_.DeviceID) - $($_.VolumeName) - Size: $([math]::Round($_.Size / 1GB, 2)) GB"
}
Write-Host ""

# Method 2: Registry fix
Write-Host "=== Method 1: Registry Fix ==="
Write-Host "Checking registry for write protection..."

$regPath = "HKLM:\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies"
if (Test-Path $regPath) {
    $writeProtect = Get-ItemProperty -Path $regPath -Name "WriteProtect" -ErrorAction SilentlyContinue
    if ($writeProtect -and $writeProtect.WriteProtect -eq 1) {
        Write-Host "Found write protection in registry. Fixing..."
        Set-ItemProperty -Path $regPath -Name "WriteProtect" -Value 0
        Write-Host "Registry fixed. Please remove and reinsert USB drive."
    } else {
        Write-Host "No write protection found in registry."
    }
} else {
    Write-Host "Registry path not found. This is normal for some systems."
}
Write-Host ""

# Method 3: DiskPart commands
Write-Host "=== Method 2: DiskPart Commands ==="
Write-Host "To manually fix with DiskPart, run these commands as Administrator:"
Write-Host ""
Write-Host "diskpart"
Write-Host "list disk"
Write-Host "select disk X  (where X is your USB drive number - BE CAREFUL!)"
Write-Host "attributes disk clear readonly"
Write-Host "clean"
Write-Host "create partition primary"
Write-Host "active"
Write-Host "format fs=ntfs quick"
Write-Host "assign"
Write-Host "exit"
Write-Host ""
Write-Host "⚠️  WARNING: This will erase all data on the USB drive!"
Write-Host ""

# Method 4: Alternative locations for backup
Write-Host "=== Alternative Backup Locations ==="
Write-Host ""
Write-Host "If USB continues to have issues, you can backup to:"
Write-Host ""

# Check for other drives
Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3 -and $_.DeviceID -ne "C:"} | ForEach-Object {
    $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
    $sizeGB = [math]::Round($_.Size / 1GB, 2)
    Write-Host "Drive $($_.DeviceID) - $freeGB GB free of $sizeGB GB total"
    
    if ($freeGB -gt 5) {
        Write-Host "  ✅ Sufficient space for migration backup (~4GB needed)"
        Write-Host "  Command: Copy-Item 'C:\ProxmoxMigration' '$($_.DeviceID)\ProxmoxMigration' -Recurse -Force"
    } else {
        Write-Host "  ❌ Insufficient space for migration backup"
    }
    Write-Host ""
}

# Method 5: Network backup option
Write-Host "=== Network Backup Option ==="
Write-Host "You can also backup to a network location:"
Write-Host "1. Share a folder on another computer"
Write-Host "2. Use: Copy-Item 'C:\ProxmoxMigration' '\\ComputerName\SharedFolder\ProxmoxMigration' -Recurse -Force"
Write-Host ""

# Method 6: Cloud backup
Write-Host "=== Cloud Backup Option ==="
Write-Host "Since you have Nextcloud, you could backup there:"
Write-Host "Copy-Item 'C:\ProxmoxMigration' 'C:\Users\<USER>\Nextcloud3\ProxmoxMigration' -Recurse -Force"
Write-Host ""

Write-Host "=== Quick Fix Attempt ==="
Write-Host "Attempting to create the registry key if it doesn't exist..."

try {
    if (!(Test-Path $regPath)) {
        New-Item -Path $regPath -Force | Out-Null
        Write-Host "Created registry path."
    }
    
    Set-ItemProperty -Path $regPath -Name "WriteProtect" -Value 0 -Type DWord
    Write-Host "Set WriteProtect to 0."
    Write-Host ""
    Write-Host "✅ Registry fix applied. Please:"
    Write-Host "1. Remove USB drive"
    Write-Host "2. Wait 5 seconds"
    Write-Host "3. Reinsert USB drive"
    Write-Host "4. Try copying files again"
} catch {
    Write-Host "❌ Could not modify registry. Please run as Administrator."
}

Write-Host ""
Write-Host "=== If All Else Fails ==="
Write-Host "Try a different USB drive, or use one of the alternative backup methods above."
