.rgthree-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.08s ease-in-out;
  color: #dde;
  background-color: #111;
  font-size: 12px;
  box-shadow: 0 0 10px black !important;
}
.rgthree-menu > li {
  position: relative;
  padding: 4px 6px;
  z-index: 9999;
  white-space: nowrap;
}
.rgthree-menu > li[role=button] {
  background-color: var(--comfy-menu-bg) !important;
  color: var(--input-text);
  cursor: pointer;
}
.rgthree-menu > li[role=button]:hover {
  filter: brightness(155%);
}
.rgthree-menu[state^=measuring] {
  display: block;
  opacity: 0;
}
.rgthree-menu[state=open] {
  display: block;
  opacity: 1;
  pointer-events: all;
}

.rgthree-top-menu {
  box-sizing: border-box;
  white-space: nowrap;
  background: var(--content-bg);
  color: var(--content-fg);
  display: flex;
  flex-direction: column;
}
.rgthree-top-menu * {
  box-sizing: inherit;
}
.rgthree-top-menu menu {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rgthree-top-menu menu > li:not(#fakeid) {
  list-style: none;
  padding: 0;
  margin: 0;
}
.rgthree-top-menu menu > li:not(#fakeid) > button {
  cursor: pointer;
  padding: 8px 12px 8px 8px;
  width: 100%;
  text-align: start;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: start;
}
.rgthree-top-menu menu > li:not(#fakeid) > button:hover {
  background-color: var(--comfy-input-bg);
}
.rgthree-top-menu menu > li:not(#fakeid) > button svg {
  height: 16px;
  width: auto;
  margin-inline-end: 0.6em;
}
.rgthree-top-menu menu > li:not(#fakeid) > button svg.github-star {
  fill: rgb(227, 179, 65);
}
.rgthree-top-menu menu > li:not(#fakeid).rgthree-message {
  min-height: 32px;
}
.rgthree-top-menu menu > li:not(#fakeid).rgthree-message > span {
  padding: 8px 12px;
  display: block;
  width: 100%;
  text-align: center;
  font-style: italic;
  font-size: 12px;
}
