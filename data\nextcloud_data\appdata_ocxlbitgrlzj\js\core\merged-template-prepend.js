/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2015 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 */

/**
 * Namespace to hold functions related to convert mimetype to icons
 *
 * @namespace
 */
OC.MimeType = {

	/**
	 * Cache that maps mimeTypes to icon urls
	 */
	_mimeTypeIcons: {},

	/**
	 * Return the file icon we want to use for the given mimeType.
	 * The file needs to be present in the supplied file list
	 *
	 * @param {string} mimeType The mimeType we want an icon for
	 * @param {array} files The available icons in this theme
	 * @return {string} The icon to use or null if there is no match
	 */
	_getFile: function(mimeType, files) {
		var icon = mimeType.replace(new RegExp('/', 'g'), '-');

		// Generate path
		if (mimeType === 'dir' && files.includes('folder')) {
			return 'folder';
		} else if (mimeType === 'dir-encrypted' && files.includes('folder-encrypted')) {
			return 'folder-encrypted';
		} else if (mimeType === 'dir-shared' && files.includes('folder-shared')) {
			return 'folder-shared';
		} else if (mimeType === 'dir-public' && files.includes('folder-public')) {
			return 'folder-public';
		} else if ((mimeType === 'dir-external' || mimeType === 'dir-external-root') && files.includes('folder-external')) {
			return 'folder-external';
		} else if (files.includes(icon)) {
			return icon;
		} else if (files.includes(icon.split('-')[0])) {
			return icon.split('-')[0];
		} else if (files.includes('file')) {
			return 'file';
		}

		return null;
	},

	/**
	 * Return the url to icon of the given mimeType
	 *
	 * @param {string} mimeType The mimeType to get the icon for
	 * @return {string} Url to the icon for mimeType
	 */
	getIconUrl: function(mimeType) {
		if (typeof mimeType === 'undefined') {
			return undefined;
		}

		while (mimeType in OC.MimeTypeList.aliases) {
			mimeType = OC.MimeTypeList.aliases[mimeType];
		}

		if (mimeType in OC.MimeType._mimeTypeIcons) {
			return OC.MimeType._mimeTypeIcons[mimeType];
		}

		// First try to get the correct icon from the current theme
		var gotIcon = null;
		var path = '';
		if (OC.theme.folder !== '' && Array.isArray(OC.MimeTypeList.themes[OC.theme.folder])) {
			path = OC.getRootPath() + '/themes/' + OC.theme.folder + '/core/img/filetypes/';
			var icon = OC.MimeType._getFile(mimeType, OC.MimeTypeList.themes[OC.theme.folder]);

			if (icon !== null) {
				gotIcon = true;
				path += icon;
			}
		}
		if(OCA.Theming && gotIcon === null) {
			path = OC.generateUrl('/apps/theming/img/core/filetypes/');
			path += OC.MimeType._getFile(mimeType, OC.MimeTypeList.files);
			gotIcon = true;
		}

		// If we do not yet have an icon fall back to the default
		if (gotIcon === null) {
			path = OC.getRootPath() + '/core/img/filetypes/';
			path += OC.MimeType._getFile(mimeType, OC.MimeTypeList.files);
		}

		path += '.svg';

		if(OCA.Theming) {
			path += "?v=" + OCA.Theming.cacheBuster;
		}

		// Cache the result
		OC.MimeType._mimeTypeIcons[mimeType] = path;
		return path;
	}

};


/**
* This file is automatically generated
* DO NOT EDIT MANUALLY!
*
* You can update the list of MimeType Aliases in config/mimetypealiases.json
* The list of files is fetched from core/img/filetypes
* To regenerate this file run ./occ maintenance:mimetype:update-js
*/
OC.MimeTypeList={
	aliases: {
    "application/coreldraw": "image",
    "application/epub+zip": "text",
    "application/font-sfnt": "font",
    "application/font-woff": "font",
    "application/gpx+xml": "location",
    "application/gzip": "package/x-generic",
    "application/illustrator": "image",
    "application/internet-shortcut": "link",
    "application/javascript": "text/code",
    "application/json": "text/code",
    "application/km": "mindmap",
    "application/msaccess": "file",
    "application/msexcel": "x-office/spreadsheet",
    "application/msonenote": "x-office/document",
    "application/mspowerpoint": "x-office/presentation",
    "application/msword": "x-office/document",
    "application/octet-stream": "file",
    "application/postscript": "image",
    "application/rss+xml": "application/xml",
    "application/vnd.android.package-archive": "package/x-generic",
    "application/vnd.excalidraw+json": "whiteboard",
    "application/vnd.garmin.tcx+xml": "location",
    "application/vnd.google-earth.kml+xml": "location",
    "application/vnd.google-earth.kmz": "location",
    "application/vnd.lotus-wordpro": "x-office/document",
    "application/vnd.ms-excel.addin.macroEnabled.12": "x-office/spreadsheet",
    "application/vnd.ms-excel.sheet.binary.macroEnabled.12": "x-office/spreadsheet",
    "application/vnd.ms-excel.sheet.macroEnabled.12": "x-office/spreadsheet",
    "application/vnd.ms-excel.template.macroEnabled.12": "x-office/spreadsheet",
    "application/vnd.ms-excel": "x-office/spreadsheet",
    "application/vnd.ms-fontobject": "font",
    "application/vnd.ms-powerpoint.addin.macroEnabled.12": "x-office/presentation",
    "application/vnd.ms-powerpoint.presentation.macroEnabled.12": "x-office/presentation",
    "application/vnd.ms-powerpoint.slideshow.macroEnabled.12": "x-office/presentation",
    "application/vnd.ms-powerpoint.template.macroEnabled.12": "x-office/presentation",
    "application/vnd.ms-powerpoint": "x-office/presentation",
    "application/vnd.ms-visio.drawing.macroEnabled.12": "application/vnd.visio",
    "application/vnd.ms-visio.drawing": "application/vnd.visio",
    "application/vnd.ms-visio.stencil.macroEnabled.12": "application/vnd.visio",
    "application/vnd.ms-visio.stencil": "application/vnd.visio",
    "application/vnd.ms-visio.template.macroEnabled.12": "application/vnd.visio",
    "application/vnd.ms-visio.template": "application/vnd.visio",
    "application/vnd.ms-word.document.macroEnabled.12": "x-office/document",
    "application/vnd.ms-word.template.macroEnabled.12": "x-office/document",
    "application/vnd.oasis.opendocument.graphics-flat-xml": "x-office/drawing",
    "application/vnd.oasis.opendocument.graphics-template": "x-office/drawing",
    "application/vnd.oasis.opendocument.graphics": "x-office/drawing",
    "application/vnd.oasis.opendocument.presentation-flat-xml": "x-office/presentation",
    "application/vnd.oasis.opendocument.presentation-template": "x-office/presentation",
    "application/vnd.oasis.opendocument.presentation": "x-office/presentation",
    "application/vnd.oasis.opendocument.spreadsheet-flat-xml": "x-office/spreadsheet",
    "application/vnd.oasis.opendocument.spreadsheet-template": "x-office/spreadsheet",
    "application/vnd.oasis.opendocument.spreadsheet": "x-office/spreadsheet",
    "application/vnd.oasis.opendocument.text-flat-xml": "x-office/document",
    "application/vnd.oasis.opendocument.text-master": "x-office/document",
    "application/vnd.oasis.opendocument.text-template": "x-office/document",
    "application/vnd.oasis.opendocument.text-web": "x-office/document",
    "application/vnd.oasis.opendocument.text": "x-office/document",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "x-office/presentation",
    "application/vnd.openxmlformats-officedocument.presentationml.slideshow": "x-office/presentation",
    "application/vnd.openxmlformats-officedocument.presentationml.template": "x-office/presentation",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "x-office/spreadsheet",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.template": "x-office/spreadsheet",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document.docxf": "x-office/form-template",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document.oform": "x-office/form",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "x-office/document",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.template": "x-office/document",
    "application/vnd.visio": "x-office/document",
    "application/vnd.wordperfect": "x-office/document",
    "application/vnd.xmind.workbook": "mindmap",
    "application/x-7z-compressed": "package/x-generic",
    "application/x-bzip2": "package/x-generic",
    "application/x-cbr": "text",
    "application/x-compressed": "package/x-generic",
    "application/x-dcraw": "image",
    "application/x-deb": "package/x-generic",
    "application/x-fictionbook+xml": "text",
    "application/x-font": "font",
    "application/x-freemind": "mindmap",
    "application/x-gimp": "image",
    "application/x-gzip": "application/gzip",
    "application/x-iwork-keynote-sffkey": "x-office/presentation",
    "application/x-iwork-numbers-sffnumbers": "x-office/spreadsheet",
    "application/x-iwork-pages-sffpages": "x-office/document",
    "application/x-mobipocket-ebook": "text",
    "application/x-perl": "text/code",
    "application/x-photoshop": "image",
    "application/x-php": "text/code",
    "application/x-rar-compressed": "package/x-generic",
    "application/x-tar": "package/x-generic",
    "application/x-tex": "text",
    "application/xml": "text/html",
    "application/yaml": "text/code",
    "application/zip": "package/x-generic",
    "application/zstd": "package/x-generic",
    "database": "file",
    "httpd/unix-directory": "dir",
    "image/targa": "image/tga",
    "image/x-emf": "image/emf",
    "text/css": "text/code",
    "text/csv": "x-office/spreadsheet",
    "text/html": "text/code",
    "text/x-c": "text/code",
    "text/x-c++src": "text/code",
    "text/x-h": "text/code",
    "text/x-java-source": "text/code",
    "text/x-ldif": "text/code",
    "text/x-python": "text/code",
    "text/x-rst": "text",
    "text/x-shellscript": "text/code",
    "web": "text/code"
},
	files: [
    "application",
    "application-pdf",
    "audio",
    "file",
    "folder",
    "folder-drag-accept",
    "folder-encrypted",
    "folder-external",
    "folder-public",
    "folder-shared",
    "folder-starred",
    "font",
    "image",
    "link",
    "location",
    "mindmap",
    "package-x-generic",
    "text",
    "text-calendar",
    "text-code",
    "text-vcard",
    "video",
    "whiteboard",
    "x-office-document",
    "x-office-drawing",
    "x-office-form",
    "x-office-form-template",
    "x-office-presentation",
    "x-office-spreadsheet"
],
	themes: []
};


/**
 * SPDX-FileCopyrightText: 2016 ownCloud Inc.
 * SPDX-License-Identifier: AGPL-3.0-or-later
 */

/* global Select2 */

/**
 * Select2 extension for toggling values in a multi-select dropdown
 */
(function(Select2) {

	var Select2FindHighlightableChoices = Select2.class.multi.prototype.findHighlightableChoices;
	Select2.class.multi.prototype.findHighlightableChoices = function () {
		if (this.opts.toggleSelect) {
			return this.results.find('.select2-result-selectable:not(.select2-disabled)');
		}
		return Select2FindHighlightableChoices.apply(this, arguments);
	};

	var Select2TriggerSelect = Select2.class.multi.prototype.triggerSelect;
	Select2.class.multi.prototype.triggerSelect = function (data) {
		if (this.opts.toggleSelect && this.val().indexOf(this.id(data)) !== -1) {
			var self = this;
			var val = this.id(data);

			var selectionEls = this.container.find('.select2-search-choice').filter(function() {
				return (self.id($(this).data('select2-data')) === val);
			});

			if (this.unselect(selectionEls)) {
				// also unselect in dropdown
				this.results.find('.select2-result.select2-selected').each(function () {
					var $this = $(this);
					if (self.id($this.data('select2-data')) === val) {
						$this.removeClass('select2-selected');
					}
				});
				this.clearSearch();
			}

			return false;
		} else {
			return Select2TriggerSelect.apply(this, arguments);
		}
	};

})(Select2);



