{
  "compilerOptions": {
    "target": "es2019",
    "module": "ESNext",
    // "typeRoots": [
    //   "./ts/typings",
    // ],
    "baseUrl": "./",
    "paths": {
      // `@comfyorg/litegraph` is how ComfyUi-Frontend augments litegraph.d.ts, so we should match.
      "@comfyorg/litegraph": ["node_modules/@comfyorg/litegraph/dist/litegraph.d.ts"],
      // ComfyUi-Frontend also augments one other module with the dist/ so we must keep it.
      "@comfyorg/litegraph/dist/*": ["node_modules/@comfyorg/litegraph/dist/*"],
      // Theser are just vanity. Currently nothing else augments it but, if a dependency were to we
      // may need to change these.
      "@comfyorg/frontend": ["node_modules/@comfyorg/comfyui-frontend-types/index.d.ts"],
      "typings/*": ["src_web/typings/*"],
      "rgthree/common/*": ["src_web/common/*"],
      "node_modules": ["node_modules/*"],
      "scripts/*":  ["src_web/scripts_comfy/*"],
    },
    "outDir": "web/",
    "removeComments": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "exactOptionalPropertyTypes": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "allowUnusedLabels": true,
    "skipLibCheck": true,
  },
  "include": [
    "src_web/*.ts", "src_web/**/*.ts", "src_web/typings/index.d.ts",
  ],
  "exclude": [
    "**/*.spec.ts",
    "**/*.d.ts",
    "node_modules/**/*.ts"
  ]
}
