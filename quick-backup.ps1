# Quick Critical Files Backup
# Alternative to full system backup

$backupPath = "D:\ProxmoxMigration\critical-files-backup"
Write-Host "Creating critical files backup..." -ForegroundColor Green

New-Item -ItemType Directory -Path $backupPath -Force | Out-Null

# Backup user profile
Write-Host "Backing up user profile..."
$userBackup = "$backupPath\UserProfile"
New-Item -ItemType Directory -Path $userBackup -Force | Out-Null

# Documents, Desktop, Downloads
robocopy "$env:USERPROFILE\Documents" "$userBackup\Documents" /MIR /XD "node_modules" "__pycache__" ".git" /R:1 /W:1
robocopy "$env:USERPROFILE\Desktop" "$userBackup\Desktop" /MIR /R:1 /W:1
robocopy "$env:USERPROFILE\Downloads" "$userBackup\Downloads" /MIR /XF "*.tmp" "*.temp" /R:1 /W:1

# Application settings (selective)
Write-Host "Backing up application settings..."
$appDataBackup = "$userBackup\AppData"
New-Item -ItemType Directory -Path $appDataBackup -Force | Out-Null

# Common application data folders
$appFolders = @(
    "$env:APPDATA\Code",
    "$env:APPDATA\Microsoft\Windows\PowerShell",
    "$env:LOCALAPPDATA\Programs\Microsoft VS Code"
)

foreach ($folder in $appFolders) {
    if (Test-Path $folder) {
        $folderName = Split-Path $folder -Leaf
        robocopy $folder "$appDataBackup\$folderName" /MIR /XD "Cache" "cache" "logs" "temp" /R:1 /W:1
    }
}

Write-Host "Critical files backup complete!" -ForegroundColor Green
Write-Host "Location: $backupPath"
