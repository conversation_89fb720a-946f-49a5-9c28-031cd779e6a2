# Windows System Backup Script
# Creates a backup of your Windows installation for VM conversion

$migrationPath = "C:\ProxmoxMigration"
$backupPath = "$migrationPath\windows-backup"

Write-Host "=== Windows System Backup for Proxmox Migration ==="
Write-Host ""

# Create backup directory
New-Item -ItemType Directory -Path $backupPath -Force

# Check available drives for backup destination
Write-Host "Available drives for backup:"
Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3} | ForEach-Object {
    $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
    $sizeGB = [math]::Round($_.Size / 1GB, 2)
    Write-Host "$($_.DeviceID) - $freeGB GB free of $sizeGB GB total"
}
Write-Host ""

# Get system information
$systemInfo = Get-ComputerInfo
Write-Host "System Information:"
Write-Host "OS: $($systemInfo.WindowsProductName)"
Write-Host "Version: $($systemInfo.WindowsVersion)"
Write-Host "Total RAM: $([math]::Round($systemInfo.TotalPhysicalMemory / 1GB, 2)) GB"
Write-Host ""

# Create system info file
$systemInfo | Out-File "$backupPath\system-info.txt"

# Method 1: Using Disk2VHD (Recommended)
Write-Host "=== Method 1: Disk2VHD (Recommended) ==="
Write-Host "1. Download Disk2VHD from Microsoft Sysinternals:"
Write-Host "   https://docs.microsoft.com/en-us/sysinternals/downloads/disk2vhd"
Write-Host ""
Write-Host "2. Run as Administrator:"
Write-Host "   disk2vhd.exe * E:\ProxmoxMigration\windows-vm.vhdx"
Write-Host "   (Replace E: with your backup drive)"
Write-Host ""

# Method 2: Using DISM (Alternative)
Write-Host "=== Method 2: DISM Image (Alternative) ==="
Write-Host "Run as Administrator:"
Write-Host "dism /capture-image /imagefile:E:\ProxmoxMigration\windows.wim /capturedir:C:\ /name:`"Windows System`""
Write-Host ""

# Method 3: File-level backup (Fallback)
Write-Host "=== Method 3: Critical Files Backup (Fallback) ==="
Write-Host "If full system backup isn't possible, backup these critical directories:"

$criticalDirs = @(
    "C:\Users\<USER>\Documents",
    "C:\Users\<USER>\Desktop", 
    "C:\Users\<USER>\Downloads",
    "C:\Users\<USER>\AppData\Roaming",
    "C:\ProgramData"
)

foreach ($dir in $criticalDirs) {
    if (Test-Path $dir) {
        Write-Host "- $dir"
    }
}
Write-Host ""

# Create backup script for critical files
$backupScript = @"
# Critical Files Backup Script
`$backupDrive = "E:"  # Change to your backup drive
`$userBackup = "`$backupDrive\ProxmoxMigration\user-backup"

New-Item -ItemType Directory -Path `$userBackup -Force

# Backup user directories
robocopy "C:\Users\<USER>\Documents" "`$userBackup\Documents" /MIR /XD "node_modules" "__pycache__" ".git"
robocopy "C:\Users\<USER>\Desktop" "`$userBackup\Desktop" /MIR
robocopy "C:\Users\<USER>\Downloads" "`$userBackup\Downloads" /MIR /XF "*.tmp" "*.temp"

# Backup application data (selective)
robocopy "C:\Users\<USER>\AppData\Roaming" "`$userBackup\AppData\Roaming" /MIR /XD "Temp" "Cache" "cache" "logs"

Write-Host "Critical files backup complete!"
"@

$backupScript | Out-File "$backupPath\backup-critical-files.ps1"

# Create VM import instructions
$vmInstructions = @"
# VM Import Instructions for Proxmox

## After creating Windows backup:

1. Copy backup files to Proxmox host:
   scp windows-vm.vhdx root@proxmox-ip:/var/lib/vz/images/

2. Create VM in Proxmox:
   - VM ID: 100
   - Name: Windows-Dev
   - OS: Windows 10/11
   - CPU: 8 cores (i9-12900K has 16 cores total)
   - RAM: 16GB (you have 64GB total)
   - Network: vmbr0

3. Import disk:
   qm importdisk 100 /var/lib/vz/images/windows-vm.vhdx vm-fast --format qcow2

4. Attach disk to VM:
   - Go to VM Hardware tab
   - Add imported disk as SATA/SCSI drive
   - Set as boot disk

5. Configure GPU passthrough:
   - Add PCI device (RTX 3090 or GTX 1060)
   - Enable IOMMU in BIOS first
   - Configure VFIO drivers

6. Start VM and install drivers:
   - GPU drivers
   - VirtIO drivers for better performance
   - Proxmox guest agent

## VM Configuration for your hardware:
- Host CPU: Intel i9-12900K (16 cores)
- Host RAM: 64GB DDR5
- VM allocation: 8 cores, 16GB RAM
- GPU: RTX 3090 passthrough
- Storage: vm-fast pool (Samsung 980 Pro)
"@

$vmInstructions | Out-File "$backupPath\vm-import-instructions.txt"

Write-Host "=== Backup Preparation Complete! ==="
Write-Host ""
Write-Host "Files created in: $backupPath"
Write-Host "- system-info.txt (system specifications)"
Write-Host "- backup-critical-files.ps1 (fallback file backup)"
Write-Host "- vm-import-instructions.txt (Proxmox import guide)"
Write-Host ""
Write-Host "=== Next Steps ==="
Write-Host "1. Choose backup method (Disk2VHD recommended)"
Write-Host "2. Copy entire C:\ProxmoxMigration folder to external drive"
Write-Host "3. Boot into Proxmox"
Write-Host "4. Run storage setup script"
Write-Host "5. Import Windows VM"
Write-Host ""
Write-Host "=== Summary of Migration Files ==="
Write-Host "Total backup size: ~$(Get-ChildItem C:\ProxmoxMigration -Recurse | Measure-Object -Property Length -Sum | ForEach-Object {[math]::Round($_.Sum / 1MB, 2)}) MB"
