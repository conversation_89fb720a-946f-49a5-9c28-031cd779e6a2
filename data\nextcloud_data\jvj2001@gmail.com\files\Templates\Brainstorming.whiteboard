{"type": "excalidraw", "version": 2, "source": "https://excalidraw.com", "elements": [{"type": "text", "version": 383, "versionNonce": 1510296125, "index": "ag", "isDeleted": false, "id": "YwAf_yRnWyc2PX35y_XUI", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 472.1991217628704, "y": -190.22473552541393, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 158.9499969482422, "height": 19.2, "seed": 581609107, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719258809310, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Problem statement", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Problem statement", "autoResize": true, "lineHeight": 1.2}, {"type": "text", "version": 651, "versionNonce": 750596893, "index": "ah", "isDeleted": false, "id": "I9DU-W1K6aNHYXqP90ggI", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 476.1991217628704, "y": 8.451713300578557, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 177.64999389648438, "height": 19.2, "seed": 1024969725, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Ideas and solutions", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Ideas and solutions", "autoResize": true, "lineHeight": 1.2}, {"type": "text", "version": 754, "versionNonce": 1459250045, "index": "ahV", "isDeleted": false, "id": "j4OQPgO89Qje2VXezct43", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 845.5177757402613, "y": -190.22473552541393, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 102.8499984741211, "height": 19.2, "seed": 639322771, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719258953823, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Constraints", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Constraints", "autoResize": true, "lineHeight": 1.2}, {"type": "text", "version": 1017, "versionNonce": 1865996595, "index": "ai", "isDeleted": false, "id": "yVWNbAGHX17fCrwkvTz4D", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 782.9420371001244, "y": 8.451713300578557, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 84.1500015258789, "height": 19.2, "seed": 415834653, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Strengths", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Strengths", "autoResize": true, "lineHeight": 1.2}, {"type": "text", "version": 1225, "versionNonce": 1614989181, "index": "aj", "isDeleted": false, "id": "0iX8LYeTsVP1l-T2s1N_H", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1008.9068368364061, "y": 8.451713300578557, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 93.5, "height": 19.2, "seed": 1549863613, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Weaknesses", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Weaknesses", "autoResize": true, "lineHeight": 1.2}, {"type": "text", "version": 1131, "versionNonce": 1928381917, "index": "ajV", "isDeleted": false, "id": "PMM8JXfn0NMQ5LW-zRC2-", "fillStyle": "hachure", "strokeWidth": 1, "strokeStyle": "solid", "roughness": 1, "opacity": 100, "angle": 0, "x": 1213.906836836406, "y": 8.451713300578557, "strokeColor": "#1e1e1e", "backgroundColor": "transparent", "width": 93.5, "height": 19.2, "seed": 989161267, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719258934466, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Next steps", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Next steps", "autoResize": true, "lineHeight": 1.2}, {"type": "rectangle", "version": 556, "versionNonce": 254960989, "index": "al", "isDeleted": false, "id": "VjW9qN_ThM_fG7wGEi3zy", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 466.1991217628704, "y": -163.99432263878413, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 371.41551228463504, "height": 134.62152190943564, "seed": 755041875, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"type": "text", "id": "2cOLYYm7A_61FCMwn1Zvv"}], "updated": 1719258838575, "link": null, "locked": false}, {"type": "text", "version": 163, "versionNonce": 1621973043, "index": "al8", "isDeleted": false, "id": "2cOLYYm7A_61FCMwn1Zvv", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 471.1991217628704, "y": -158.99432263878413, "strokeColor": "#868e96", "backgroundColor": "#a5d8ff", "width": 252.4499969482422, "height": 19.2, "seed": 1442644915, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719258809310, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Type your problem statement", "textAlign": "left", "verticalAlign": "top", "containerId": "VjW9qN_ThM_fG7wGEi3zy", "originalText": "Type your problem statement", "autoResize": true, "lineHeight": 1.2}, {"type": "rectangle", "version": 909, "versionNonce": 1983052605, "index": "alG", "isDeleted": false, "id": "zQ8jCiLXCSavvpBgGhxaz", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 840.0034349119279, "y": -163.99432263878413, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 371.4882919050018, "height": 134.62152190943564, "seed": 1883861725, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"type": "text", "id": "3oWb9DIN4M4UdA8jDHtOe"}], "updated": 1719283715564, "link": null, "locked": false}, {"type": "text", "version": 410, "versionNonce": 1996170003, "index": "alO", "isDeleted": false, "id": "3oWb9DIN4M4UdA8jDHtOe", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 845.0034349119279, "y": -158.99432263878413, "strokeColor": "#868e96", "backgroundColor": "#a5d8ff", "width": 252.4499969482422, "height": 19.2, "seed": 717674803, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283715564, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Add constraints and context", "textAlign": "left", "verticalAlign": "top", "containerId": "zQ8jCiLXCSavvpBgGhxaz", "originalText": "Add constraints and context", "autoResize": true, "lineHeight": 1.2}, {"type": "rectangle", "version": 748, "versionNonce": 1338850003, "index": "ao", "isDeleted": false, "id": "AFfcSPQwSlngJr4y4034t", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 470.72201101662796, "y": 35.07752533451233, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 738.361670675924, "height": 108.44511487148984, "seed": 1700215773, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"type": "text", "id": "9KfbWqmXD71voGE9bp_RJ"}], "updated": 1719283680506, "link": null, "locked": false}, {"type": "text", "version": 688, "versionNonce": 1255591901, "index": "ao2", "isDeleted": false, "id": "9KfbWqmXD71voGE9bp_RJ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 475.72201101662796, "y": 40.07752533451233, "strokeColor": "#868e96", "backgroundColor": "#a5d8ff", "width": 663.8499755859375, "height": 19.2, "seed": 975495645, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Put sticky notes with your ideas, their strengths, and their weaknesses", "textAlign": "left", "verticalAlign": "top", "containerId": "AFfcSPQwSlngJr4y4034t", "originalText": "Put sticky notes with your ideas, their strengths, and their weaknesses", "autoResize": true, "lineHeight": 1.2}, {"type": "rectangle", "version": 767, "versionNonce": 245655667, "index": "ap", "isDeleted": false, "id": "7yDhKLkzBfSF5bRc66oVl", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 470.72201101662796, "y": 146.76558511644717, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 738.361670675924, "height": 108.44511487148984, "seed": 105869363, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 814, "versionNonce": 1787481149, "index": "aq", "isDeleted": false, "id": "Q9SEOHNMa7EXJmH9qe-cv", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 470.72201101662796, "y": 258.453644898382, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 738.361670675924, "height": 108.44511487148984, "seed": 658677501, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 959, "versionNonce": 1904534035, "index": "ar", "isDeleted": false, "id": "cGEOBAqnuC3A7-l5XlBst", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 470.72201101662796, "y": 369.5101513866009, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 738.361670675924, "height": 108.44511487148984, "seed": 1904625213, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 997, "versionNonce": 1235940595, "index": "arV", "isDeleted": false, "id": "H_CqrE52rh6dd5hw8ZD_s", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1212.0075252306065, "y": 34.864016959404125, "strokeColor": "transparent", "backgroundColor": "#e9ecef", "width": 182.30648015042343, "height": 443.1494168156073, "seed": 2111118195, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [{"type": "text", "id": "fOQP0zBhNhkOoDceFLBtP"}], "updated": 1719258973630, "link": null, "locked": false}, {"type": "text", "version": 37, "versionNonce": 1525339827, "index": "arl", "isDeleted": false, "id": "fOQP0zBhNhkOoDceFLBtP", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 1217.0075252306065, "y": 39.864016959404125, "strokeColor": "#868e96", "backgroundColor": "#a5d8ff", "width": 168.3000030517578, "height": 38.4, "seed": 783727453, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719259021903, "link": null, "locked": false, "fontSize": 16, "fontFamily": 3, "text": "Steps to be taken \ngoing forward", "textAlign": "left", "verticalAlign": "top", "containerId": "H_CqrE52rh6dd5hw8ZD_s", "originalText": "Steps to be taken going forward", "autoResize": true, "lineHeight": 1.2}, {"type": "rectangle", "version": 474, "versionNonce": 2134916253, "index": "au", "isDeleted": false, "id": "DzbSEqBrLbb3lKcQrsFxx", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 265.49481319810616, "y": -179.17487401291376, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1645549531, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 495, "versionNonce": 1097148339, "index": "av", "isDeleted": false, "id": "WZEom92zqdfWB3bvDangJ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 282.99863505708635, "y": -161.6710521539336, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 462033333, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 518, "versionNonce": 1353228541, "index": "aw", "isDeleted": false, "id": "nZ8mZHPHC5VZ5uoWqtxpo", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 300.5024569160665, "y": -144.1672302949534, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 405866811, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 542, "versionNonce": 1684781395, "index": "ax", "isDeleted": false, "id": "9J0KBWG9K-Bn-mt561JH-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 318.0062787750467, "y": -125.20475661439156, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1120639189, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 636, "versionNonce": 613361651, "index": "ay", "isDeleted": false, "id": "TB5OKpbpdAI77RREw4NP-", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 334.0514488124452, "y": -107.70093475541137, "strokeColor": "#1e1e1e", "backgroundColor": "#ffc9c9", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1618263227, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283685989, "link": null, "locked": false}, {"type": "rectangle", "version": 611, "versionNonce": 938415859, "index": "b03", "isDeleted": false, "id": "YVJqqtlgvijhMp3CYDCnQ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 265.49481319810616, "y": -10.96788127726191, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 510791861, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 632, "versionNonce": 1399342525, "index": "b04", "isDeleted": false, "id": "QEDMpqMrqNg-ed3bqotZJ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 282.9986350570864, "y": 6.535940581718279, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1174159643, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 652, "versionNonce": 712360083, "index": "b05", "isDeleted": false, "id": "LEUaaj6StBGjj59sf4YFE", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 300.5024569160666, "y": 24.03976244069844, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1471689237, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 675, "versionNonce": 825394717, "index": "b06", "isDeleted": false, "id": "RTsuXxz1M3_r1fDCNFMO0", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 318.0062787750469, "y": 43.002236121260324, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1152083387, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 821, "versionNonce": 364262963, "index": "b07", "isDeleted": false, "id": "-mXbI1hozwou-POaGtPQr", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 334.0514488124453, "y": 60.506057980240485, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 980625269, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 718, "versionNonce": 1888860797, "index": "b08", "isDeleted": false, "id": "6ymZV4DyQakiS3L_zQQj3", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 265.49481319810616, "y": 157.23911145838997, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1606442517, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 739, "versionNonce": 1486288851, "index": "b09", "isDeleted": false, "id": "M7GhNo6WsfIyw43O7jRIE", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 282.9986350570864, "y": 174.74293331737016, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1099692475, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 760, "versionNonce": 1515414237, "index": "b0A", "isDeleted": false, "id": "UCxfsrQQO-B-yngsrKXD5", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 300.5024569160665, "y": 192.2467551763503, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1754649461, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 782, "versionNonce": 825132403, "index": "b0B", "isDeleted": false, "id": "EH4XO1dsZo1aDUyW4jUNo", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 318.0062787750468, "y": 211.20922885691215, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1929054811, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 802, "versionNonce": 587005757, "index": "b0C", "isDeleted": false, "id": "y-CKZPtQsFjW2MBowgtAs", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 334.0514488124452, "y": 228.7130507158924, "strokeColor": "#1e1e1e", "backgroundColor": "#b2f2bb", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 366320853, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283680506, "link": null, "locked": false}, {"type": "rectangle", "version": 777, "versionNonce": 1963158365, "index": "b0D", "isDeleted": false, "id": "7UYO3r8Gz3EK9CuACzWLB", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 265.49481319810616, "y": 325.4461041940419, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1101524691, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719256694709, "link": null, "locked": false}, {"type": "rectangle", "version": 798, "versionNonce": 1606501619, "index": "b0E", "isDeleted": false, "id": "fTDljLJxvlleiXmpVIwKZ", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 282.9986350570864, "y": 342.949926053022, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 3234781, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719256694709, "link": null, "locked": false}, {"type": "rectangle", "version": 819, "versionNonce": 1805121469, "index": "b0F", "isDeleted": false, "id": "3MUytbxPXUODVsfbrayTM", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 300.5024569160665, "y": 360.45374791200226, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 1527290995, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719256694709, "link": null, "locked": false}, {"type": "rectangle", "version": 841, "versionNonce": 869412499, "index": "b0G", "isDeleted": false, "id": "c5137UgSK_I_hfnR9OYgN", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 318.0062787750468, "y": 379.4162215925641, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 749983805, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719256694709, "link": null, "locked": false}, {"type": "rectangle", "version": 861, "versionNonce": 1285290013, "index": "b0H", "isDeleted": false, "id": "LjAcWGlNBlr0PLbG4W5OS", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 334.0514488124452, "y": 396.92004345154425, "strokeColor": "#1e1e1e", "backgroundColor": "#a5d8ff", "width": 68.30657117576682, "height": 68.30657117576682, "seed": 188913171, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719256694709, "link": null, "locked": false}, {"type": "text", "version": 34, "versionNonce": 736101533, "index": "b0I", "isDeleted": false, "id": "dCgNeDt0YFLuySMnefDKq", "fillStyle": "solid", "strokeWidth": 2, "strokeStyle": "solid", "roughness": 0, "opacity": 100, "angle": 0, "x": 467.93533541137526, "y": -271.76852769908936, "strokeColor": "#1e1e1e", "backgroundColor": "#ffec99", "width": 345.9750061035156, "height": 33.6, "seed": 183218579, "groupIds": [], "frameId": null, "roundness": null, "boundElements": [], "updated": 1719283728017, "link": null, "locked": false, "fontSize": 28, "fontFamily": 3, "text": "Brainstorming session", "textAlign": "left", "verticalAlign": "top", "containerId": null, "originalText": "Brainstorming session", "autoResize": true, "lineHeight": 1.2}], "appState": {"gridSize": null, "viewBackgroundColor": "#ffffff"}, "files": {}}