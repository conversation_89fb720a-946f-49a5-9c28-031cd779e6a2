# Changelog

| Version         | Change                                                                                                                                                                                                                                                                                                                                                                 |
| --------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 3.24.5          | Update .npmignore to drop 2 mb of test files. Thanks [Misha Kaletsky](https://github.com/mmkal)!                                                                                                                                                                                                                                                                       |
| 3.24.4          | Added options to set the value of additionalProperties in objects and record                                                                                                                                                                                                                                                                                           |
| 3.24.3          | Adds postProcess callback option                                                                                                                                                                                                                                                                                                                                       |
| 3.24.2          | Restructured internals to remove circular dependencies which apparently might cause some build systems to whine a bit. Big thanks to [Víctor Hernández](https://github.com/NanezX) for the fix.                                                                                                                                                                        |
| 3.24.1          | Adds OpenAI target                                                                                                                                                                                                                                                                                                                                                     |
| 3.24.0          | Implements new string checks (jwt, base64url, cidr ipv4/v6), matching the new Zod version                                                                                                                                                                                                                                                                              |
| 3.23.5          | Module import hotfix by [Enzo Monjardín](https://github.com/enzomonjardin). Thanks!                                                                                                                                                                                                                                                                                    |
| 3.23.4          | Fixes branded regex property names and a weird edgecase in arrays. Thanks to [Isaiah Marc Sanchez](https://github.com/imsanchez) and [Mitchell Merry](https://github.com/mitchell-merry)!                                                                                                                                                                              |
| 3.23.3          | More tests (Thanks [Brett Zamir!](https://github.com/brettz9)), removed dead code                                                                                                                                                                                                                                                                                      |
| 3.23.2          | Lazily loads Emoji regex to avoid incompatibility with some environments. Thanks [Jacob Lee](https://github.com/jacoblee93)!                                                                                                                                                                                                                                           |
| 3.23.1          | Best-effort RegEx flag support by [Spappz](https://github.com/Spappz)! Some minor fixes and additions, such as the title option.                                                                                                                                                                                                                                       |
| 3.23.0          | Adds support for base64, date, time, duration and nanoid string validations. A warm welcome and a big thanks to [Colin](https://www.github.com/colinhacks), the creator of Zod, joining in as a contributor :)                                                                                                                                                         |
| 3.22.5          | Adds new z.date() parsing options and override callback                                                                                                                                                                                                                                                                                                                |
| 3.22.4          | Adds fix for nullable references in OpenAPI mode                                                                                                                                                                                                                                                                                                                       |
| 3.22.3          | Adjust root path from "#/" to "#" according to RFC 6901                                                                                                                                                                                                                                                                                                                |
| 3.22.2          | Adds "output" pipe strategy                                                                                                                                                                                                                                                                                                                                            |
| 3.22.1          | Fixes broken imports when using some bundlers                                                                                                                                                                                                                                                                                                                          |
| 3.22.0          | Support readonly. Export both CJS and ESM. Export everything from index. Alternative map parser. Improved pattern handling and updated sources.                                                                                                                                                                                                                        |
| 3.21.4          | Fixes missing support for exact array length                                                                                                                                                                                                                                                                                                                           |
| 3.21.3          | Fixes issue #77 (Reference path to nullable schemas in Open-API mode)                                                                                                                                                                                                                                                                                                  |
| 3.21.2          | Adds "integer" type Date output to support min/max checks, markdownDescription option, fixes "none" refStrategy by adding "seen" and adds an option to use "pattern" with Zods' email enum instead of "format".                                                                                                                                                        |
| 3.21.1          | New target (2019-09) along with improved intersection schemas, improved mutual recursion references in definitions, descriptions respected in union parser and not removed in collapsed                                                                                                                                                                                |
| 3.21.0          | Added new string validations (ip, emoji, etc) and BigInt checks to support Zod 3.21                                                                                                                                                                                                                                                                                    |
| 3.20.5          | Added uniqueItems to Set and an option to disregard pipe schemas                                                                                                                                                                                                                                                                                                       |
| 3.20.4          | Bugfixes and improved record parsing for openApi3                                                                                                                                                                                                                                                                                                                      |
| 3.20.3          | Added Cuid2 support introduced in Zod 3.20.3                                                                                                                                                                                                                                                                                                                           |
| 3.20.2          | Reintroduced conditional simplified return-type for when target is OpenAPI 3                                                                                                                                                                                                                                                                                           |
| 3.20.1          | Fixed inconsistent casing in imports                                                                                                                                                                                                                                                                                                                                   |
| 3.20.0          | Adds support for Zod 3.20 with catch and pipe parser as well as new string validations. Refactored Ref handling; adding definitions no longer considered experimental. Main API function refactored and simplified; output type less defined but a lot easier to maintain. Doubt anyone will miss it. <br/><quote><i>Narrator: Someone did in fact miss it</i></quote> |
| 3.19.4          | Adds custom error message support                                                                                                                                                                                                                                                                                                                                      |
| 3.19.3          | Mark `definitions` as experimental in the readme                                                                                                                                                                                                                                                                                                                       |
| 3.19.2          | Added `definitions` option                                                                                                                                                                                                                                                                                                                                             |
| 3.19.1          | Strict unions fix                                                                                                                                                                                                                                                                                                                                                      |
| 3.19.0          | No new features added in Zod, parity bump                                                                                                                                                                                                                                                                                                                              |
| 3.18.2          | Fixes support for native enums                                                                                                                                                                                                                                                                                                                                         |
| 3.18.1          | Add strictUnions options                                                                                                                                                                                                                                                                                                                                               |
| 3.18.0          | Added support for branded types                                                                                                                                                                                                                                                                                                                                        |
| 3.17.2          | Fix for reference paths when supplying name option string.                                                                                                                                                                                                                                                                                                             |
| 3.17.1          | Added startsWith and endsWith string checks. Merge multiple pattern checks into allOf array.                                                                                                                                                                                                                                                                           |
| 3.17.0          | Added switch case handler for new trim "check". No changes to functionality.                                                                                                                                                                                                                                                                                           |
| 3.15.x - 3.16.x | Skipped: Did not change the Zod API in any way relevant for this package.                                                                                                                                                                                                                                                                                              |
| 3.14.1          | Dependabot security updates                                                                                                                                                                                                                                                                                                                                            |
| 3.14.0          | Moves Zod into peerDependencies. Supports checks for sets, NaN-type (sort of), discriminated union type and standalone optional properties (as unions with undefined)                                                                                                                                                                                                  |
| 3.12.x - 3.13.x | Skipped                                                                                                                                                                                                                                                                                                                                                                |
| 3.11.3          | Joins unions of enums into single enum and allows enums as keys of records                                                                                                                                                                                                                                                                                             |
| 3.11.2          | Adds option to target Open API 3 spec (paths) instead of Json Schema 7.                                                                                                                                                                                                                                                                                                |
| 3.11.1          | Performance boost when using $refStrategy `none` and internal improvements.                                                                                                                                                                                                                                                                                            |
| 3.11.0          | Added description support introduced in Zod 3.11.5                                                                                                                                                                                                                                                                                                                     |
| 3.10.x          | Skipped: Minor 10 did not change the Zod API                                                                                                                                                                                                                                                                                                                           |
| 3.9.5           | Type bug fix: used dev dependency types in package                                                                                                                                                                                                                                                                                                                     |
| 3.9.4           | Path bug fix and test case when using optional definitions path                                                                                                                                                                                                                                                                                                        |
| 3.9.3           | Added option to change definition property name to $defs                                                                                                                                                                                                                                                                                                               |
| 3.9.2           | Added option to handle transform results as any instead of relying on their input schema.                                                                                                                                                                                                                                                                              |
| 3.9.1           | Refactored the way reference pointers are passed around and added options pattern to main function without braking backwards compatibility! You can now add a base path, change the reference strategy (or opt out), and still set the schema name inside the options object or outside as before.                                                                     |
| 3.9.0           | Added support for multipleOf number validaion, .rest() schemas for tuples and key validation for records (only compatible with string keys due to JSON Schema limitation).                                                                                                                                                                                             |
| 3.7.x - 3.8.x   | Skipped to reach functional parity with Zod versioning.                                                                                                                                                                                                                                                                                                                |
| 3.6.1           | Realised intersection had another potential ref pathing bug. Fixed.                                                                                                                                                                                                                                                                                                    |
| 3.6.0           | Added support for default & effects (refine). Broke out changelog.md                                                                                                                                                                                                                                                                                                   |
| 3.5.0           | Added support for CUID string validation                                                                                                                                                                                                                                                                                                                               |
| 3.4.3           | Fixed $ref pathing for catchall and intersection. Additional tests and code structure fixes.                                                                                                                                                                                                                                                                           |
| 3.4.2           | Fixed broken intersection parser (Thanks [Noah2610](https://github.com/Noah2610)!)                                                                                                                                                                                                                                                                                     |
| 3.4.1           | Fixed pathing bug for nullable items.                                                                                                                                                                                                                                                                                                                                  |
| 3.4.0           | Added support for z.lazy()                                                                                                                                                                                                                                                                                                                                             |
| 3.3.0           | Added support for catchall (additionalProperties schema on objects). Rebuilt object parser.                                                                                                                                                                                                                                                                            |
| 3.2.0           | Added support for Map and Set as seen by their most common JSON definitions. Beware no standardized definition seem to exist and JSON.parse doesn't handle either natively. Their implementations here are based on the spread approach. Also further simplified intersection definition to just allOf.                                                                |
| 3.1.0           | String patterns finally supported! Fixed bugs include broken external type, unsafe nullable parsing, bad intersection implementation, and missing support for passthrough keys in objects.                                                                                                                                                                             |
| 3.0.3           | Fixed array deep pathing bug (path contained `array` instead of `items`)                                                                                                                                                                                                                                                                                               |
| 3.0.2           | Fixed broken type usage (NonEmptyArrayDefinition was removed from Zod)                                                                                                                                                                                                                                                                                                 |
| 3.0.1           | Fixed a typo in the readme                                                                                                                                                                                                                                                                                                                                             |
| 3.0.0           | Compatible with Zod 3.2.0. Huge props to [Mr Hammad Asif](https://github.com/mrhammadasif) for his work on this.                                                                                                                                                                                                                                                       |
| 0.6.2           | Hotfix for undefined object properties. Could crash the parser when using Pick                                                                                                                                                                                                                                                                                         |
| 0.6.1           | Fixed bug in union pathing. `$Ref` was missing `/anyOf`                                                                                                                                                                                                                                                                                                                |
| 0.6.0           | Moved `@types/json-schema` and `typescript` to dev dependencies. `@types/json-schema` is now only used for the test suites. Using `strict: true` in ts config.                                                                                                                                                                                                         |
| 0.5.1           | First working release with all relevant Zod types present with most validations (except for string patterns due to Zod not exposing the source regexp pattern for those).                                                                                                                                                                                              |
| < 0.5.1         | Deprecated due to broken package structure. Please be patient, I eat crayons.                                                                                                                                                                                                                                                                                          |
