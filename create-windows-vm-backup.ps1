# Windows VM Backup Creation Script
# Creates a backup of your Windows system for Proxmox VM conversion

Write-Host "=== Windows VM Backup Creation ===" -ForegroundColor Green
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host "✅ Running as Administrator" -ForegroundColor Green
Write-Host ""

# Set backup location
$backupPath = "D:\ProxmoxMigration\windows-vm-backup"
Write-Host "Creating backup directory: $backupPath"
New-Item -ItemType Directory -Path $backupPath -Force | Out-Null

# Get system information
Write-Host "Gathering system information..." -ForegroundColor Cyan
$computerInfo = Get-ComputerInfo
$osInfo = Get-WmiObject -Class Win32_OperatingSystem
$diskInfo = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DriveType -eq 3}

Write-Host ""
Write-Host "=== System Information ===" -ForegroundColor Yellow
Write-Host "OS: $($osInfo.Caption)"
Write-Host "Version: $($osInfo.Version)"
Write-Host "Architecture: $($osInfo.OSArchitecture)"
Write-Host "Total RAM: $([math]::Round($computerInfo.TotalPhysicalMemory / 1GB, 2)) GB"
Write-Host "Computer Name: $($env:COMPUTERNAME)"
Write-Host ""

# Show available drives and space
Write-Host "=== Available Drives ===" -ForegroundColor Yellow
$diskInfo | ForEach-Object {
    $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
    $sizeGB = [math]::Round($_.Size / 1GB, 2)
    $usedGB = $sizeGB - $freeGB
    Write-Host "$($_.DeviceID) - $usedGB GB used, $freeGB GB free of $sizeGB GB total"
}
Write-Host ""

# Calculate estimated backup size (C: drive used space)
$cDrive = $diskInfo | Where-Object {$_.DeviceID -eq "C:"}
$estimatedSizeGB = [math]::Round(($cDrive.Size - $cDrive.FreeSpace) / 1GB, 2)
Write-Host "Estimated backup size: ~$estimatedSizeGB GB (C: drive used space)" -ForegroundColor Cyan
Write-Host ""

# Check if D: drive has enough space
$dDrive = $diskInfo | Where-Object {$_.DeviceID -eq "D:"}
if ($dDrive -and ($dDrive.FreeSpace / 1GB) -gt ($estimatedSizeGB + 10)) {
    Write-Host "✅ D: drive has sufficient space for backup" -ForegroundColor Green
} else {
    Write-Host "⚠️  Warning: D: drive may not have enough space" -ForegroundColor Yellow
    Write-Host "Consider using a different drive or external storage" -ForegroundColor Yellow
}
Write-Host ""

# Method 1: Disk2VHD Instructions
Write-Host "=== Method 1: Disk2VHD (Recommended) ===" -ForegroundColor Green
Write-Host ""
Write-Host "1. Download Disk2VHD from the opened browser window"
Write-Host "2. Extract and run as Administrator"
Write-Host "3. Use these settings:"
Write-Host "   - Select: C: (and any other drives you want to include)"
Write-Host "   - Output file: $backupPath\windows-system.vhdx"
Write-Host "   - Check 'Use VHDX' for better performance"
Write-Host "   - Check 'Use volume shadow copy' for consistent backup"
Write-Host ""

# Create Disk2VHD command
$disk2vhdCommand = "disk2vhd.exe C: `"$backupPath\windows-system.vhdx`""
Write-Host "Command to run in Disk2VHD directory:" -ForegroundColor Cyan
Write-Host $disk2vhdCommand -ForegroundColor White
Write-Host ""

# Method 2: PowerShell alternative (for reference)
Write-Host "=== Method 2: PowerShell Alternative ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "If Disk2VHD doesn't work, you can try:"
Write-Host "1. Create system image with Windows built-in tools"
Write-Host "2. Use third-party tools like Clonezilla"
Write-Host ""

# Create VM import instructions
$vmInstructions = @"
# Proxmox VM Import Instructions

## After creating Windows backup:

### 1. Copy backup to Proxmox host:
scp "$backupPath\windows-system.vhdx" root@proxmox-ip:/var/lib/vz/images/

### 2. Create VM in Proxmox Web Interface:
- VM ID: 100
- Name: Windows-Dev
- OS Type: Microsoft Windows
- Version: 10/2016/2019/2022
- Machine: q35
- BIOS: OVMF (UEFI)
- CPU: host (8 cores recommended)
- Memory: 16384 MB (16GB)
- Network: vmbr0

### 3. Import disk via Proxmox shell:
qm importdisk 100 /var/lib/vz/images/windows-system.vhdx vm-fast --format qcow2

### 4. Attach disk to VM:
- Go to VM Hardware tab
- Add imported disk as SATA0 or SCSI0
- Set as boot disk
- Add EFI disk if using UEFI

### 5. Configure GPU passthrough (optional):
- Add PCI device (RTX 3090 or GTX 1060)
- Enable IOMMU in BIOS first
- Configure VFIO drivers

### 6. Start VM and install drivers:
- VirtIO drivers for better performance
- GPU drivers if using passthrough
- Proxmox guest agent

### Your hardware allocation:
- Host: i9-12900K (16 cores), 64GB RAM
- VM: 8 cores, 16GB RAM
- Storage: vm-fast pool (Samsung 980 Pro)
- GPU: Optional RTX 3090 passthrough
"@

$vmInstructions | Out-File "$backupPath\vm-import-guide.txt"

# Create backup verification script
$verifyScript = @"
# Backup Verification Script
Write-Host "Verifying Windows VM backup..."

`$backupFile = "$backupPath\windows-system.vhdx"
if (Test-Path `$backupFile) {
    `$fileSize = (Get-Item `$backupFile).Length / 1GB
    Write-Host "✅ Backup file exists: `$([math]::Round(`$fileSize, 2)) GB"
    
    # Basic file integrity check
    try {
        `$stream = [System.IO.File]::OpenRead(`$backupFile)
        `$stream.Close()
        Write-Host "✅ Backup file is readable"
    } catch {
        Write-Host "❌ Backup file may be corrupted: `$_"
    }
} else {
    Write-Host "❌ Backup file not found at `$backupFile"
}
"@

$verifyScript | Out-File "$backupPath\verify-backup.ps1"

# Save system info to file
$systemInfo = @"
Windows System Information for VM Creation
==========================================

OS: $($osInfo.Caption)
Version: $($osInfo.Version)
Architecture: $($osInfo.OSArchitecture)
Computer Name: $($env:COMPUTERNAME)
Total RAM: $([math]::Round($computerInfo.TotalPhysicalMemory / 1GB, 2)) GB
Backup Date: $(Get-Date)
Estimated Size: $estimatedSizeGB GB

Disk Configuration:
$($diskInfo | ForEach-Object {
    $freeGB = [math]::Round($_.FreeSpace / 1GB, 2)
    $sizeGB = [math]::Round($_.Size / 1GB, 2)
    "$($_.DeviceID) - $sizeGB GB total, $freeGB GB free"
} | Out-String)

Recommended VM Settings:
- CPU: 8 cores (host type)
- RAM: 16GB
- Storage: 100GB+ (based on C: drive usage)
- Network: vmbr0
- GPU: Optional RTX 3090 passthrough
"@

$systemInfo | Out-File "$backupPath\system-info.txt"

Write-Host "=== Backup Preparation Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "Files created in: $backupPath" -ForegroundColor Cyan
Write-Host "- vm-import-guide.txt (Proxmox import instructions)"
Write-Host "- verify-backup.ps1 (backup verification script)"
Write-Host "- system-info.txt (system specifications)"
Write-Host ""
Write-Host "=== Next Steps ===" -ForegroundColor Yellow
Write-Host "1. Download and run Disk2VHD as Administrator"
Write-Host "2. Create backup using the command shown above"
Write-Host "3. Run verify-backup.ps1 to check the backup"
Write-Host "4. Boot into Proxmox when ready"
Write-Host ""
Write-Host "Estimated backup time: 30-60 minutes (depending on drive speed)"
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
