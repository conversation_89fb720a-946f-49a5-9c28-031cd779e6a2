# local image with smart cache
docker build -t agent-zero-run:local --build-arg BRANCH=development --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S)  .

# local image without cache
docker build -t agent-zero-run:local --build-arg BRANCH=development --no-cache  .

# local image from Kali
docker build -f ./DockerfileKali -t agent-zero-run:hacking --build-arg BRANCH=main --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S) .

# dockerhub push:

docker login

# development:
docker buildx build --build-arg BRANCH=development -t frdel/agent-zero-run:development --platform linux/amd64,linux/arm64 --push --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S) .

# testing:
docker buildx build --build-arg BRANCH=testing -t frdel/agent-zero-run:testing --platform linux/amd64,linux/arm64 --push --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S) .

# main
docker buildx build --build-arg BRANCH=main -t frdel/agent-zero-run:vx.x.x  -t frdel/agent-zero-run:latest --platform linux/amd64,linux/arm64 --push --build-arg CACHE_DATE=$(date +%Y-%m-%d:%H:%M:%S) .


# plain output
--progress=plain